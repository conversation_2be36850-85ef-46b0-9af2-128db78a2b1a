@echo off
echo ========================================
echo   نظام إدارة التبرعات والمحتاجين
echo ========================================
echo.

echo جاري فتح المشروع في Visual Studio 2022 Preview...
echo.

REM فتح المشروع في Visual Studio 2022 Preview
start "" "C:\Program Files\Microsoft Visual Studio\2022\Preview\Common7\IDE\devenv.exe" "M\M.vbproj"

echo.
echo تم فتح المشروع في Visual Studio 2022 Preview
echo.
echo خطوات التشغيل:
echo 1. انتظر حتى يتم تحميل المشروع كاملاً
echo 2. اضغط F5 أو اختر Debug > Start Debugging
echo 3. أو اضغط Ctrl+F5 لتشغيل بدون تصحيح
echo.
echo بيانات تسجيل الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo ملاحظة: تأكد من تشغيل SQL Server قبل تشغيل البرنامج
echo.

pause
