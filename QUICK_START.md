# دليل التشغيل السريع - نظام إدارة التبرعات والمحتاجين

## 🚀 خطوات التشغيل السريع

### 1. تشغيل ملف RunProgram.bat
- انقر نقراً مزدوجاً على ملف `RunProgram.bat`
- سيتم فتح المشروع في Visual Studio 2022 Preview تلقائياً

### 2. في Visual Studio 2022 Preview
1. **انتظر تحميل المشروع** (قد يستغرق دقيقة أو دقيقتين)
2. **اضغط F5** أو اختر `Debug > Start Debugging`
3. أو **اضغط Ctrl+F5** للتشغيل بدون تصحيح

### 3. تسجيل الدخول
```
اسم المستخدم: admin
كلمة المرور: admin123
```

## ⚠️ متطلبات مهمة

### قبل التشغيل تأكد من:
- ✅ تشغيل **SQL Server** أو **SQL Server Express**
- ✅ وجود **Visual Studio 2022 Preview**
- ✅ تثبيت **.NET Framework 4.7.2**

### إذا واجهت مشاكل:

#### مشكلة: خطأ في الاتصال بقاعدة البيانات
**الحل:**
1. تأكد من تشغيل SQL Server
2. افتح SQL Server Management Studio
3. شغل ملف `Database\CharitySystemDB.sql`

#### مشكلة: لم يفتح Visual Studio
**الحل:**
1. تأكد من مسار Visual Studio في الملف
2. أو افتح Visual Studio يدوياً
3. اختر `File > Open > Project/Solution`
4. اختر ملف `M\M.vbproj`

#### مشكلة: أخطاء في البناء (Build Errors)
**الحل:**
1. اختر `Build > Rebuild Solution`
2. تأكد من وجود جميع الملفات
3. تحقق من المراجع (References)

## 🎯 الوظائف الرئيسية

### بعد تسجيل الدخول ستجد:
1. **لوحة التحكم** - إحصائيات ومعلومات عامة
2. **تسجيل محتاج جديد** - إضافة طلبات جديدة
3. **تسجيل تبرع جديد** - تسجيل التبرعات
4. **إدارة المحتاجين** - الموافقة/رفض الطلبات
5. **إدارة التبرعات** - متابعة التبرعات
6. **التقارير** - تقارير شاملة
7. **إدارة المستخدمين** - إضافة موظفين

## 📞 الدعم الفني

### إذا احتجت مساعدة:
1. راجع ملف `README.md` للتفاصيل الكاملة
2. راجع ملف `TESTING_GUIDE.md` لدليل الاختبار
3. تحقق من ملف `App.config` لإعدادات قاعدة البيانات

---

## 🎉 مبروك! النظام جاهز للاستخدام

بعد اتباع هذه الخطوات، ستتمكن من استخدام نظام إدارة التبرعات والمحتاجين بكامل وظائفه.
