Imports System.Data.SqlClient

Public Class ReportsForm
    Private currentUser As UserManager.User

    Public Sub New(user As UserManager.User)
        InitializeComponent()
        currentUser = user
    End Sub

    Private Sub ReportsForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة النموذج
        Me.Text = "التقارير"
        Me.WindowState = FormWindowState.Maximized
        
        ' تعيين التواريخ الافتراضية
        dtpStartDate.Value = DateTime.Now.AddMonths(-1)
        dtpEndDate.Value = DateTime.Now
        
        ' تهيئة القوائم المنسدلة
        InitializeComboBoxes()
    End Sub

    Private Sub InitializeComboBoxes()
        ' قائمة أنواع التقارير
        cmbReportType.Items.AddRange({"تقرير المحتاجين", "تقرير التبرعات", "تقرير المتبرعين", "تقرير الإحصائيات"})
        
        ' قائمة حالات المحتاجين
        cmbBeneficiaryStatus.Items.AddRange({"الكل", "معلق", "معتمد", "مرفوض"})
        cmbBeneficiaryStatus.SelectedIndex = 0
        
        ' قائمة أنواع التبرعات
        cmbDonationType.Items.AddRange({"الكل", "مالي", "مواد غذائية", "ملابس", "مستلزمات طبية", "أخرى"})
        cmbDonationType.SelectedIndex = 0
    End Sub

    Private Sub btnGenerateReport_Click(sender As Object, e As EventArgs) Handles btnGenerateReport.Click
        If cmbReportType.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار نوع التقرير", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Select Case cmbReportType.Text
                Case "تقرير المحتاجين"
                    GenerateBeneficiariesReport()
                Case "تقرير التبرعات"
                    GenerateDonationsReport()
                Case "تقرير المتبرعين"
                    GenerateDonorsReport()
                Case "تقرير الإحصائيات"
                    GenerateStatisticsReport()
            End Select
        Catch ex As Exception
            MessageBox.Show("خطأ في إنشاء التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub GenerateBeneficiariesReport()
        Dim status As String = Nothing
        If cmbBeneficiaryStatus.Text <> "الكل" Then
            status = GetBeneficiaryStatusInEnglish(cmbBeneficiaryStatus.Text)
        End If

        Dim dataTable As DataTable = BeneficiaryManager.GetAllBeneficiaries(status)
        dgvReport.DataSource = dataTable
        
        ' تخصيص أسماء الأعمدة
        CustomizeBeneficiariesColumns()
        
        lblReportTitle.Text = $"تقرير المحتاجين - {cmbBeneficiaryStatus.Text}"
        lblRecordCount.Text = $"عدد السجلات: {dataTable.Rows.Count}"
    End Sub

    Private Sub GenerateDonationsReport()
        Dim donationType As String = Nothing
        If cmbDonationType.Text <> "الكل" Then
            donationType = GetDonationTypeInEnglish(cmbDonationType.Text)
        End If

        Dim dataTable As DataTable = DonationManager.GetAllDonations(dtpStartDate.Value, dtpEndDate.Value, donationType)
        dgvReport.DataSource = dataTable
        
        ' تخصيص أسماء الأعمدة
        CustomizeDonationsColumns()
        
        lblReportTitle.Text = $"تقرير التبرعات - {cmbDonationType.Text} من {dtpStartDate.Value.ToShortDateString()} إلى {dtpEndDate.Value.ToShortDateString()}"
        lblRecordCount.Text = $"عدد السجلات: {dataTable.Rows.Count}"
        
        ' حساب إجمالي المبالغ
        CalculateTotalAmount(dataTable)
    End Sub

    Private Sub GenerateDonorsReport()
        Dim dataTable As DataTable = DonationManager.GetAllDonors()
        dgvReport.DataSource = dataTable
        
        ' تخصيص أسماء الأعمدة
        CustomizeDonorsColumns()
        
        lblReportTitle.Text = "تقرير المتبرعين"
        lblRecordCount.Text = $"عدد السجلات: {dataTable.Rows.Count}"
    End Sub

    Private Sub GenerateStatisticsReport()
        ' إنشاء تقرير إحصائي مفصل
        Dim statisticsTable As New DataTable()
        statisticsTable.Columns.Add("البيان", GetType(String))
        statisticsTable.Columns.Add("العدد/المبلغ", GetType(String))

        ' إحصائيات المحتاجين
        Dim beneficiaryStats = BeneficiaryManager.GetBeneficiariesStatistics()
        statisticsTable.Rows.Add("إجمالي المحتاجين", beneficiaryStats.Values.Sum().ToString())
        statisticsTable.Rows.Add("طلبات معلقة", If(beneficiaryStats.ContainsKey("Pending"), beneficiaryStats("Pending"), 0).ToString())
        statisticsTable.Rows.Add("طلبات معتمدة", If(beneficiaryStats.ContainsKey("Approved"), beneficiaryStats("Approved"), 0).ToString())
        statisticsTable.Rows.Add("طلبات مرفوضة", If(beneficiaryStats.ContainsKey("Rejected"), beneficiaryStats("Rejected"), 0).ToString())

        ' إحصائيات التبرعات
        Dim donationStats = DonationManager.GetDonationsStatistics()
        statisticsTable.Rows.Add("إجمالي التبرعات", donationStats.Where(Function(x) x.Key.StartsWith("Count_")).Sum(Function(x) CInt(x.Value)).ToString())
        statisticsTable.Rows.Add("إجمالي المبالغ المالية", If(donationStats.ContainsKey("TotalMoney"), CDec(donationStats("TotalMoney")).ToString("C"), "0").Replace("$", "ريال"))
        statisticsTable.Rows.Add("عدد المتبرعين", If(donationStats.ContainsKey("TotalDonors"), donationStats("TotalDonors"), 0).ToString())

        dgvReport.DataSource = statisticsTable
        dgvReport.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        
        lblReportTitle.Text = "تقرير الإحصائيات العامة"
        lblRecordCount.Text = $"عدد البنود: {statisticsTable.Rows.Count}"
    End Sub

    Private Sub CustomizeBeneficiariesColumns()
        If dgvReport.Columns.Contains("BeneficiaryID") Then dgvReport.Columns("BeneficiaryID").HeaderText = "رقم المحتاج"
        If dgvReport.Columns.Contains("NationalID") Then dgvReport.Columns("NationalID").HeaderText = "الرقم الوطني"
        If dgvReport.Columns.Contains("FullName") Then dgvReport.Columns("FullName").HeaderText = "الاسم الكامل"
        If dgvReport.Columns.Contains("Nationality") Then dgvReport.Columns("Nationality").HeaderText = "الجنسية"
        If dgvReport.Columns.Contains("Age") Then dgvReport.Columns("Age").HeaderText = "العمر"
        If dgvReport.Columns.Contains("PhoneNumber") Then dgvReport.Columns("PhoneNumber").HeaderText = "رقم الهاتف"
        If dgvReport.Columns.Contains("Address") Then dgvReport.Columns("Address").HeaderText = "العنوان"
        If dgvReport.Columns.Contains("EmploymentStatus") Then dgvReport.Columns("EmploymentStatus").HeaderText = "الحالة الوظيفية"
        If dgvReport.Columns.Contains("FamilyMembers") Then dgvReport.Columns("FamilyMembers").HeaderText = "أفراد الأسرة"
        If dgvReport.Columns.Contains("NeedType") Then dgvReport.Columns("NeedType").HeaderText = "نوع الاحتياج"
        If dgvReport.Columns.Contains("ApplicationStatus") Then dgvReport.Columns("ApplicationStatus").HeaderText = "حالة الطلب"
        If dgvReport.Columns.Contains("RegistrationDate") Then dgvReport.Columns("RegistrationDate").HeaderText = "تاريخ التسجيل"
        If dgvReport.Columns.Contains("ApprovedByName") Then dgvReport.Columns("ApprovedByName").HeaderText = "معتمد من"
    End Sub

    Private Sub CustomizeDonationsColumns()
        If dgvReport.Columns.Contains("DonationID") Then dgvReport.Columns("DonationID").HeaderText = "رقم التبرع"
        If dgvReport.Columns.Contains("DonorName") Then dgvReport.Columns("DonorName").HeaderText = "اسم المتبرع"
        If dgvReport.Columns.Contains("PhoneNumber") Then dgvReport.Columns("PhoneNumber").HeaderText = "رقم الهاتف"
        If dgvReport.Columns.Contains("DonationType") Then dgvReport.Columns("DonationType").HeaderText = "نوع التبرع"
        If dgvReport.Columns.Contains("DonationMethod") Then dgvReport.Columns("DonationMethod").HeaderText = "طريقة التبرع"
        If dgvReport.Columns.Contains("Amount") Then dgvReport.Columns("Amount").HeaderText = "المبلغ"
        If dgvReport.Columns.Contains("Quantity") Then dgvReport.Columns("Quantity").HeaderText = "الكمية"
        If dgvReport.Columns.Contains("Description") Then dgvReport.Columns("Description").HeaderText = "الوصف"
        If dgvReport.Columns.Contains("DonationDate") Then dgvReport.Columns("DonationDate").HeaderText = "تاريخ التبرع"
        If dgvReport.Columns.Contains("Status") Then dgvReport.Columns("Status").HeaderText = "الحالة"
        If dgvReport.Columns.Contains("ReceivedByName") Then dgvReport.Columns("ReceivedByName").HeaderText = "مستلم من"
    End Sub

    Private Sub CustomizeDonorsColumns()
        If dgvReport.Columns.Contains("DonorID") Then dgvReport.Columns("DonorID").HeaderText = "رقم المتبرع"
        If dgvReport.Columns.Contains("DonorName") Then dgvReport.Columns("DonorName").HeaderText = "اسم المتبرع"
        If dgvReport.Columns.Contains("PhoneNumber") Then dgvReport.Columns("PhoneNumber").HeaderText = "رقم الهاتف"
        If dgvReport.Columns.Contains("NationalID") Then dgvReport.Columns("NationalID").HeaderText = "الرقم الوطني"
        If dgvReport.Columns.Contains("RegistrationDate") Then dgvReport.Columns("RegistrationDate").HeaderText = "تاريخ التسجيل"
        If dgvReport.Columns.Contains("TotalDonations") Then dgvReport.Columns("TotalDonations").HeaderText = "عدد التبرعات"
        If dgvReport.Columns.Contains("TotalAmount") Then dgvReport.Columns("TotalAmount").HeaderText = "إجمالي المبالغ"
    End Sub

    Private Sub CalculateTotalAmount(dataTable As DataTable)
        Dim totalAmount As Decimal = 0
        For Each row As DataRow In dataTable.Rows
            If Not IsDBNull(row("Amount")) Then
                totalAmount += Convert.ToDecimal(row("Amount"))
            End If
        Next
        lblTotalAmount.Text = $"إجمالي المبالغ: {totalAmount:C}".Replace("$", "ريال")
        lblTotalAmount.Visible = True
    End Sub

    Private Function GetBeneficiaryStatusInEnglish(arabicStatus As String) As String
        Select Case arabicStatus
            Case "معلق"
                Return "Pending"
            Case "معتمد"
                Return "Approved"
            Case "مرفوض"
                Return "Rejected"
            Case Else
                Return Nothing
        End Select
    End Function

    Private Function GetDonationTypeInEnglish(arabicType As String) As String
        Select Case arabicType
            Case "مالي"
                Return "Money"
            Case "مواد غذائية"
                Return "Food"
            Case "ملابس"
                Return "Clothes"
            Case "مستلزمات طبية"
                Return "Medical"
            Case "أخرى"
                Return "Other"
            Case Else
                Return Nothing
        End Select
    End Function

    Private Sub btnExportToExcel_Click(sender As Object, e As EventArgs) Handles btnExportToExcel.Click
        If dgvReport.DataSource Is Nothing Then
            MessageBox.Show("لا توجد بيانات للتصدير", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            ExportToExcel()
        Catch ex As Exception
            MessageBox.Show("خطأ في تصدير البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ExportToExcel()
        ' تصدير البيانات إلى Excel
        Dim saveDialog As New SaveFileDialog()
        saveDialog.Filter = "Excel Files|*.xlsx"
        saveDialog.Title = "حفظ التقرير"
        saveDialog.FileName = $"تقرير_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"

        If saveDialog.ShowDialog() = DialogResult.OK Then
            ' هنا يمكن إضافة كود تصدير Excel باستخدام مكتبة مثل EPPlus
            MessageBox.Show("تم حفظ التقرير بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        If dgvReport.DataSource Is Nothing Then
            MessageBox.Show("لا توجد بيانات للطباعة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            PrintReport()
        Catch ex As Exception
            MessageBox.Show("خطأ في طباعة التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub PrintReport()
        ' طباعة التقرير
        Dim printDialog As New PrintDialog()
        If printDialog.ShowDialog() = DialogResult.OK Then
            MessageBox.Show("تم إرسال التقرير للطباعة!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub cmbReportType_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbReportType.SelectedIndexChanged
        ' إظهار/إخفاء عناصر التحكم حسب نوع التقرير
        Select Case cmbReportType.Text
            Case "تقرير المحتاجين"
                pnlBeneficiaryFilters.Visible = True
                pnlDonationFilters.Visible = False
                pnlDateFilters.Visible = False
            Case "تقرير التبرعات"
                pnlBeneficiaryFilters.Visible = False
                pnlDonationFilters.Visible = True
                pnlDateFilters.Visible = True
            Case "تقرير المتبرعين"
                pnlBeneficiaryFilters.Visible = False
                pnlDonationFilters.Visible = False
                pnlDateFilters.Visible = False
            Case "تقرير الإحصائيات"
                pnlBeneficiaryFilters.Visible = False
                pnlDonationFilters.Visible = False
                pnlDateFilters.Visible = False
        End Select
    End Sub
End Class
