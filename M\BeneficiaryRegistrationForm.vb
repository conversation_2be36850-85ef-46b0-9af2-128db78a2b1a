Public Class BeneficiaryRegistrationForm
    Private currentUser As UserManager.User

    Public Sub New(user As UserManager.User)
        InitializeComponent()
        currentUser = user
    End Sub

    Private Sub BeneficiaryRegistrationForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة النموذج
        Me.Text = "تسجيل محتاج جديد"
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized

        ' تهيئة القوائم المنسدلة
        InitializeComboBoxes()
        
        ' تعيين التركيز على أول حقل
        txtNationalID.Focus()
    End Sub

    Private Sub InitializeComboBoxes()
        ' قائمة الجنسيات
        cmbNationality.Items.AddRange({"سعودي", "مصري", "سوري", "يمني", "أردني", "لبناني", "فلسطيني", "عراقي", "سوداني", "مغربي", "تونسي", "جزائري", "ليبي", "أخرى"})
        
        ' قائمة الحالة الوظيفية
        cmbEmploymentStatus.Items.AddRange({"عاطل عن العمل", "موظف", "متقاعد", "طالب", "ربة منزل", "عامل يومي", "أخرى"})
        
        ' قائمة نوع الاحتياج
        cmbNeedType.Items.AddRange({"مساعدة مالية", "مواد غذائية", "ملابس", "أدوية ومستلزمات طبية", "مستلزمات تعليمية", "أجهزة طبية", "أخرى"})
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        ' التحقق من صحة البيانات
        If Not ValidateInput() Then
            Return
        End If

        ' إنشاء كائن المحتاج
        Dim beneficiary As New BeneficiaryManager.Beneficiary() With {
            .NationalID = txtNationalID.Text.Trim(),
            .FullName = txtFullName.Text.Trim(),
            .Nationality = cmbNationality.Text,
            .Age = Convert.ToInt32(numAge.Value),
            .PhoneNumber = txtPhoneNumber.Text.Trim(),
            .Address = txtAddress.Text.Trim(),
            .EmploymentStatus = cmbEmploymentStatus.Text,
            .Workplace = txtWorkplace.Text.Trim(),
            .FamilyMembers = Convert.ToInt32(numFamilyMembers.Value),
            .NeedType = cmbNeedType.Text,
            .HealthCondition = txtHealthCondition.Text.Trim(),
            .MonthlyIncome = numMonthlyIncome.Value
        }

        ' حفظ البيانات
        If BeneficiaryManager.RegisterBeneficiary(beneficiary) Then
            MessageBox.Show("تم تسجيل المحتاج بنجاح! سيتم مراجعة الطلب من قبل المسؤول.", "نجح التسجيل", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ClearForm()
        End If
    End Sub

    Private Function ValidateInput() As Boolean
        ' التحقق من الرقم الوطني
        If String.IsNullOrWhiteSpace(txtNationalID.Text) Then
            MessageBox.Show("يرجى إدخال الرقم الوطني", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtNationalID.Focus()
            Return False
        End If

        If txtNationalID.Text.Length < 10 Then
            MessageBox.Show("الرقم الوطني يجب أن يكون 10 أرقام على الأقل", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtNationalID.Focus()
            Return False
        End If

        ' التحقق من الاسم
        If String.IsNullOrWhiteSpace(txtFullName.Text) Then
            MessageBox.Show("يرجى إدخال الاسم الكامل", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtFullName.Focus()
            Return False
        End If

        ' التحقق من العمر
        If numAge.Value < 1 Or numAge.Value > 120 Then
            MessageBox.Show("يرجى إدخال عمر صحيح", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            numAge.Focus()
            Return False
        End If

        ' التحقق من عدد أفراد الأسرة
        If numFamilyMembers.Value < 1 Then
            MessageBox.Show("يرجى إدخال عدد أفراد الأسرة", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            numFamilyMembers.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub ClearForm()
        ' مسح جميع الحقول
        txtNationalID.Clear()
        txtFullName.Clear()
        cmbNationality.SelectedIndex = -1
        numAge.Value = 1
        txtPhoneNumber.Clear()
        txtAddress.Clear()
        cmbEmploymentStatus.SelectedIndex = -1
        txtWorkplace.Clear()
        numFamilyMembers.Value = 1
        cmbNeedType.SelectedIndex = -1
        txtHealthCondition.Clear()
        numMonthlyIncome.Value = 0

        ' تعيين التركيز على أول حقل
        txtNationalID.Focus()
    End Sub

    Private Sub btnClear_Click(sender As Object, e As EventArgs) Handles btnClear.Click
        If MessageBox.Show("هل تريد مسح جميع البيانات؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            ClearForm()
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub txtNationalID_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtNationalID.KeyPress
        ' السماح بالأرقام فقط
        If Not Char.IsDigit(e.KeyChar) AndAlso Not Char.IsControl(e.KeyChar) Then
            e.Handled = True
        End If
    End Sub

    Private Sub txtPhoneNumber_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtPhoneNumber.KeyPress
        ' السماح بالأرقام والرموز فقط
        If Not Char.IsDigit(e.KeyChar) AndAlso Not Char.IsControl(e.KeyChar) AndAlso e.KeyChar <> "+"c AndAlso e.KeyChar <> "-"c Then
            e.Handled = True
        End If
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        ' البحث عن محتاج موجود
        If String.IsNullOrWhiteSpace(txtNationalID.Text) Then
            MessageBox.Show("يرجى إدخال الرقم الوطني للبحث", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtNationalID.Focus()
            Return
        End If

        Dim beneficiary As BeneficiaryManager.Beneficiary = BeneficiaryManager.SearchByNationalID(txtNationalID.Text.Trim())
        
        If beneficiary IsNot Nothing Then
            ' عرض البيانات الموجودة
            MessageBox.Show($"المحتاج موجود بالفعل في النظام!" & vbCrLf & 
                           $"الاسم: {beneficiary.FullName}" & vbCrLf & 
                           $"الحالة: {beneficiary.ApplicationStatus}", 
                           "محتاج موجود", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
            ' ملء النموذج بالبيانات الموجودة
            FillFormWithBeneficiaryData(beneficiary)
        Else
            MessageBox.Show("لم يتم العثور على محتاج بهذا الرقم الوطني", "غير موجود", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub FillFormWithBeneficiaryData(beneficiary As BeneficiaryManager.Beneficiary)
        txtFullName.Text = beneficiary.FullName
        cmbNationality.Text = beneficiary.Nationality
        numAge.Value = beneficiary.Age
        txtPhoneNumber.Text = beneficiary.PhoneNumber
        txtAddress.Text = beneficiary.Address
        cmbEmploymentStatus.Text = beneficiary.EmploymentStatus
        txtWorkplace.Text = beneficiary.Workplace
        numFamilyMembers.Value = beneficiary.FamilyMembers
        cmbNeedType.Text = beneficiary.NeedType
        txtHealthCondition.Text = beneficiary.HealthCondition
        numMonthlyIncome.Value = beneficiary.MonthlyIncome
    End Sub

    Private Sub cmbEmploymentStatus_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbEmploymentStatus.SelectedIndexChanged
        ' إظهار/إخفاء حقل جهة العمل حسب الحالة الوظيفية
        If cmbEmploymentStatus.Text = "عاطل عن العمل" Or cmbEmploymentStatus.Text = "ربة منزل" Then
            txtWorkplace.Enabled = False
            txtWorkplace.Clear()
        Else
            txtWorkplace.Enabled = True
        End If
    End Sub
End Class
