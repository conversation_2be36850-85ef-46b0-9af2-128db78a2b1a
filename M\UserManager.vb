Imports System.Data.SqlClient
Imports System.Security.Cryptography
Imports System.Text

Public Class UserManager
    ' فئة المستخدم
    Public Class User
        Public Property UserID As Integer
        Public Property Username As String
        Public Property FullName As String
        Public Property PhoneNumber As String
        Public Property UserRole As String
        Public Property IsActive As Boolean
        Public Property CreatedDate As DateTime
        Public Property LastLogin As DateTime?
    End Class

    ' تشفير كلمة المرور
    Private Shared Function HashPassword(password As String) As String
        Using sha256Hash As SHA256 = SHA256.Create()
            Dim bytes As Byte() = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password))
            Dim builder As New StringBuilder()
            
            For i As Integer = 0 To bytes.Length - 1
                builder.Append(bytes(i).ToString("x2"))
            Next
            
            Return builder.ToString()
        End Using
    End Function

    ' تسجيل الدخول
    Public Shared Function Login(username As String, password As String) As User
        Try
            Dim hashedPassword As String = HashPassword(password)
            Dim query As String = "SELECT UserID, Username, FullName, PhoneNumber, UserRole, IsActive, CreatedDate, LastLogin " &
                                 "FROM Users WHERE Username = @Username AND Password = @Password AND IsActive = 1"
            
            Dim parameters() As SqlParameter = {
                New SqlParameter("@Username", username),
                New SqlParameter("@Password", hashedPassword)
            }
            
            Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query, parameters)
            
            If dataTable.Rows.Count > 0 Then
                Dim row As DataRow = dataTable.Rows(0)
                Dim user As New User() With {
                    .UserID = Convert.ToInt32(row("UserID")),
                    .Username = row("Username").ToString(),
                    .FullName = row("FullName").ToString(),
                    .PhoneNumber = If(row("PhoneNumber") Is DBNull.Value, "", row("PhoneNumber").ToString()),
                    .UserRole = row("UserRole").ToString(),
                    .IsActive = Convert.ToBoolean(row("IsActive")),
                    .CreatedDate = Convert.ToDateTime(row("CreatedDate")),
                    .LastLogin = If(row("LastLogin") Is DBNull.Value, Nothing, Convert.ToDateTime(row("LastLogin")))
                }
                
                ' تحديث آخر تسجيل دخول
                UpdateLastLogin(user.UserID)
                
                Return user
            End If
            
            Return Nothing
        Catch ex As Exception
            MessageBox.Show("خطأ في تسجيل الدخول: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return Nothing
        End Try
    End Function

    ' تحديث آخر تسجيل دخول
    Private Shared Sub UpdateLastLogin(userID As Integer)
        Dim query As String = "UPDATE Users SET LastLogin = GETDATE() WHERE UserID = @UserID"
        Dim parameters() As SqlParameter = {New SqlParameter("@UserID", userID)}
        DatabaseConnection.ExecuteNonQuery(query, parameters)
    End Sub

    ' إضافة مستخدم جديد (للمدير فقط)
    Public Shared Function AddUser(username As String, password As String, fullName As String, 
                                  phoneNumber As String, userRole As String) As Boolean
        Try
            ' التحقق من عدم وجود اسم المستخدم
            If IsUsernameExists(username) Then
                MessageBox.Show("اسم المستخدم موجود بالفعل!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return False
            End If
            
            Dim hashedPassword As String = HashPassword(password)
            Dim query As String = "INSERT INTO Users (Username, Password, FullName, PhoneNumber, UserRole) " &
                                 "VALUES (@Username, @Password, @FullName, @PhoneNumber, @UserRole)"
            
            Dim parameters() As SqlParameter = {
                New SqlParameter("@Username", username),
                New SqlParameter("@Password", hashedPassword),
                New SqlParameter("@FullName", fullName),
                New SqlParameter("@PhoneNumber", If(String.IsNullOrEmpty(phoneNumber), DBNull.Value, phoneNumber)),
                New SqlParameter("@UserRole", userRole)
            }
            
            Return DatabaseConnection.ExecuteNonQuery(query, parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في إضافة المستخدم: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' التحقق من وجود اسم المستخدم
    Private Shared Function IsUsernameExists(username As String) As Boolean
        Dim query As String = "SELECT COUNT(*) FROM Users WHERE Username = @Username"
        Dim parameters() As SqlParameter = {New SqlParameter("@Username", username)}
        
        Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query, parameters)
        If dataTable.Rows.Count > 0 Then
            Return Convert.ToInt32(dataTable.Rows(0)(0)) > 0
        End If
        
        Return False
    End Function

    ' الحصول على جميع المستخدمين
    Public Shared Function GetAllUsers() As DataTable
        Dim query As String = "SELECT UserID, Username, FullName, PhoneNumber, UserRole, IsActive, CreatedDate, LastLogin " &
                             "FROM Users ORDER BY CreatedDate DESC"
        Return DatabaseConnection.ExecuteQuery(query)
    End Function

    ' تحديث بيانات المستخدم
    Public Shared Function UpdateUser(userID As Integer, fullName As String, phoneNumber As String, 
                                     userRole As String, isActive As Boolean) As Boolean
        Try
            Dim query As String = "UPDATE Users SET FullName = @FullName, PhoneNumber = @PhoneNumber, " &
                                 "UserRole = @UserRole, IsActive = @IsActive WHERE UserID = @UserID"
            
            Dim parameters() As SqlParameter = {
                New SqlParameter("@UserID", userID),
                New SqlParameter("@FullName", fullName),
                New SqlParameter("@PhoneNumber", If(String.IsNullOrEmpty(phoneNumber), DBNull.Value, phoneNumber)),
                New SqlParameter("@UserRole", userRole),
                New SqlParameter("@IsActive", isActive)
            }
            
            Return DatabaseConnection.ExecuteNonQuery(query, parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث المستخدم: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' حذف مستخدم (تعطيل فقط)
    Public Shared Function DeleteUser(userID As Integer) As Boolean
        Try
            Dim query As String = "UPDATE Users SET IsActive = 0 WHERE UserID = @UserID"
            Dim parameters() As SqlParameter = {New SqlParameter("@UserID", userID)}
            
            Return DatabaseConnection.ExecuteNonQuery(query, parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في حذف المستخدم: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' تغيير كلمة المرور
    Public Shared Function ChangePassword(userID As Integer, oldPassword As String, newPassword As String) As Boolean
        Try
            ' التحقق من كلمة المرور القديمة
            Dim hashedOldPassword As String = HashPassword(oldPassword)
            Dim checkQuery As String = "SELECT COUNT(*) FROM Users WHERE UserID = @UserID AND Password = @OldPassword"
            Dim checkParameters() As SqlParameter = {
                New SqlParameter("@UserID", userID),
                New SqlParameter("@OldPassword", hashedOldPassword)
            }
            
            Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(checkQuery, checkParameters)
            If dataTable.Rows.Count = 0 OrElse Convert.ToInt32(dataTable.Rows(0)(0)) = 0 Then
                MessageBox.Show("كلمة المرور القديمة غير صحيحة!", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return False
            End If
            
            ' تحديث كلمة المرور
            Dim hashedNewPassword As String = HashPassword(newPassword)
            Dim updateQuery As String = "UPDATE Users SET Password = @NewPassword WHERE UserID = @UserID"
            Dim updateParameters() As SqlParameter = {
                New SqlParameter("@UserID", userID),
                New SqlParameter("@NewPassword", hashedNewPassword)
            }
            
            Return DatabaseConnection.ExecuteNonQuery(updateQuery, updateParameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في تغيير كلمة المرور: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' إنشاء المدير الافتراضي
    Public Shared Sub CreateDefaultAdmin()
        Try
            ' التحقق من وجود مدير
            Dim query As String = "SELECT COUNT(*) FROM Users WHERE UserRole = 'Admin'"
            Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query)
            
            If dataTable.Rows.Count > 0 AndAlso Convert.ToInt32(dataTable.Rows(0)(0)) = 0 Then
                ' إنشاء المدير الافتراضي
                AddUser("admin", "admin123", "مدير النظام", "", "Admin")
                MessageBox.Show("تم إنشاء المدير الافتراضي" & vbCrLf & "اسم المستخدم: admin" & vbCrLf & "كلمة المرور: admin123", 
                               "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في إنشاء المدير الافتراضي: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
