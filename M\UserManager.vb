Public Class UserManager
    ' فئة المستخدم مبسطة
    Public Class User
        Public Property UserID As Integer
        Public Property Username As String
        Public Property FullName As String
        Public Property UserRole As String
    End Class

    ' تسجيل الدخول مبسط
    Public Shared Function Login(username As String, password As String) As User
        ' تسجيل دخول مبسط للاختبار
        If username = "admin" AndAlso password = "admin123" Then
            Return New User() With {
                .UserID = 1,
                .Username = "admin",
                .FullName = "مدير النظام",
                .UserRole = "Admin"
            }
        End If
        Return Nothing
    End Function
End Class
