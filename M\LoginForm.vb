Public Class LoginForm
    Private currentUser As UserManager.User

    Private Sub LoginForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة النموذج
        Me.Text = "نظام إدارة التبرعات والمحتاجين - تسجيل الدخول"
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        
        ' اختبار الاتصال بقاعدة البيانات
        If Not DatabaseConnection.TestConnection() Then
            ' محاولة إنشاء قاعدة البيانات
            DatabaseConnection.CreateDatabaseIfNotExists()
        End If
        
        ' إنشاء المدير الافتراضي إذا لم يكن موجوداً
        UserManager.CreateDefaultAdmin()
        
        ' تعيين القيم الافتراضية
        txtUsername.Text = "admin"
        txtPassword.Text = "admin123"
        txtUsername.Focus()
    End Sub

    Private Sub btnLogin_Click(sender As Object, e As EventArgs) Handles btnLogin.Click
        ' التحقق من صحة البيانات
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            MessageBox.Show("يرجى إدخال اسم المستخدم", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUsername.Focus()
            Return
        End If

        If String.IsNullOrWhiteSpace(txtPassword.Text) Then
            MessageBox.Show("يرجى إدخال كلمة المرور", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPassword.Focus()
            Return
        End If

        ' محاولة تسجيل الدخول
        currentUser = UserManager.Login(txtUsername.Text.Trim(), txtPassword.Text)

        If currentUser IsNot Nothing Then
            ' نجح تسجيل الدخول
            MessageBox.Show($"مرحباً {currentUser.FullName}!", "تسجيل دخول ناجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
            ' فتح النموذج الرئيسي حسب نوع المستخدم
            If currentUser.UserRole = "Admin" Then
                Dim adminForm As New AdminMainForm(currentUser)
                adminForm.Show()
            Else
                Dim employeeForm As New EmployeeMainForm(currentUser)
                employeeForm.Show()
            End If
            
            Me.Hide()
        Else
            ' فشل تسجيل الدخول
            MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtPassword.Clear()
            txtUsername.Focus()
        End If
    End Sub

    Private Sub btnExit_Click(sender As Object, e As EventArgs) Handles btnExit.Click
        Application.Exit()
    End Sub

    Private Sub txtPassword_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtPassword.KeyPress
        If e.KeyChar = Chr(13) Then ' Enter key
            btnLogin_Click(sender, e)
        End If
    End Sub

    Private Sub txtUsername_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtUsername.KeyPress
        If e.KeyChar = Chr(13) Then ' Enter key
            txtPassword.Focus()
        End If
    End Sub

    Private Sub LoginForm_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Application.Exit()
    End Sub

    Private Sub lblForgotPassword_Click(sender As Object, e As EventArgs) Handles lblForgotPassword.Click
        MessageBox.Show("يرجى الاتصال بمدير النظام لإعادة تعيين كلمة المرور", "نسيت كلمة المرور", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub chkShowPassword_CheckedChanged(sender As Object, e As EventArgs) Handles chkShowPassword.CheckedChanged
        If chkShowPassword.Checked Then
            txtPassword.PasswordChar = ""
        Else
            txtPassword.PasswordChar = "*"
        End If
    End Sub
End Class
