Public Class LoginForm

    Private Sub LoginForm_Load(sender As Object, e As EventArgs) Handles Me.Load
        ' تهيئة النموذج
        Me.Text = "نظام إدارة التبرعات والمحتاجين - تسجيل الدخول"
        Me.StartPosition = FormStartPosition.CenterScreen

        ' تعيين القيم الافتراضية
        If txtUsername IsNot Nothing Then txtUsername.Text = "admin"
        If txtPassword IsNot Nothing Then txtPassword.Text = "admin123"
    End Sub

    Private Sub btnLogin_Click(sender As Object, e As EventArgs)
        ' التحقق من صحة البيانات
        If txtUsername Is Nothing OrElse String.IsNullOrWhiteSpace(txtUsername.Text) Then
            MessageBox.Show("يرجى إدخال اسم المستخدم", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If txtPassword Is Nothing OrElse String.IsNullOrWhiteSpace(txtPassword.Text) Then
            MessageBox.Show("يرجى إدخال كلمة المرور", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        ' تسجيل دخول مبسط
        If txtUsername.Text.Trim() = "admin" AndAlso txtPassword.Text = "admin123" Then
            MessageBox.Show("مرحباً بك في النظام!", "تسجيل دخول ناجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' فتح النموذج الرئيسي
            Dim mainForm As New Form()
            mainForm.Text = "نظام إدارة التبرعات والمحتاجين - الصفحة الرئيسية"
            mainForm.Size = New Size(800, 600)
            mainForm.StartPosition = FormStartPosition.CenterScreen

            Dim lblWelcome As New Label()
            lblWelcome.Text = "مرحباً بك في نظام إدارة التبرعات والمحتاجين" & vbCrLf & vbCrLf & "النظام جاهز للاستخدام!"
            lblWelcome.Font = New Font("Tahoma", 14, FontStyle.Bold)
            lblWelcome.TextAlign = ContentAlignment.MiddleCenter
            lblWelcome.Dock = DockStyle.Fill
            lblWelcome.RightToLeft = RightToLeft.Yes

            mainForm.Controls.Add(lblWelcome)
            mainForm.Show()
            Me.Hide()
        Else
            MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error)
            If txtPassword IsNot Nothing Then txtPassword.Clear()
        End If
    End Sub

    Private Sub btnExit_Click(sender As Object, e As EventArgs)
        Application.Exit()
    End Sub
End Class
