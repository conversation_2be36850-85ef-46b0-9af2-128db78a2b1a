Imports System.Data.SqlClient

Public Class DatabaseConnection
    Private Shared connectionString As String = "Data Source=.\SQLEXPRESS;Initial Catalog=CharitySystemDB;Integrated Security=True"

    ' الحصول على اتصال جديد بقاعدة البيانات
    Public Shared Function GetConnection() As SqlConnection
        Return New SqlConnection(connectionString)
    End Function

    ' تنفيذ استعلام وإرجاع النتائج
    Public Shared Function ExecuteQuery(query As String, Optional parameters As SqlParameter() = Nothing) As DataTable
        Dim dataTable As New DataTable()
        
        Try
            Using connection As SqlConnection = GetConnection()
                Using command As New SqlCommand(query, connection)
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If
                    
                    connection.Open()
                    Using adapter As New SqlDataAdapter(command)
                        adapter.Fill(dataTable)
                    End Using
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في قاعدة البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
        
        Return dataTable
    End Function

    ' تنفيذ أمر بدون إرجاع نتائج
    Public Shared Function ExecuteNonQuery(query As String, Optional parameters As SqlParameter() = Nothing) As Boolean
        Try
            Using connection As SqlConnection = GetConnection()
                Using command As New SqlCommand(query, connection)
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If
                    
                    connection.Open()
                    command.ExecuteNonQuery()
                    Return True
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في قاعدة البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' تنفيذ إجراء مخزن
    Public Shared Function ExecuteStoredProcedure(procedureName As String, Optional parameters As SqlParameter() = Nothing) As DataTable
        Dim dataTable As New DataTable()
        
        Try
            Using connection As SqlConnection = GetConnection()
                Using command As New SqlCommand(procedureName, connection)
                    command.CommandType = CommandType.StoredProcedure
                    
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If
                    
                    connection.Open()
                    Using adapter As New SqlDataAdapter(command)
                        adapter.Fill(dataTable)
                    End Using
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تنفيذ الإجراء المخزن: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
        
        Return dataTable
    End Function

    ' تنفيذ إجراء مخزن بدون إرجاع نتائج
    Public Shared Function ExecuteStoredProcedureNonQuery(procedureName As String, Optional parameters As SqlParameter() = Nothing) As Boolean
        Try
            Using connection As SqlConnection = GetConnection()
                Using command As New SqlCommand(procedureName, connection)
                    command.CommandType = CommandType.StoredProcedure
                    
                    If parameters IsNot Nothing Then
                        command.Parameters.AddRange(parameters)
                    End If
                    
                    connection.Open()
                    command.ExecuteNonQuery()
                    Return True
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تنفيذ الإجراء المخزن: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' اختبار الاتصال بقاعدة البيانات
    Public Shared Function TestConnection() As Boolean
        Try
            Using connection As SqlConnection = GetConnection()
                connection.Open()
                Return True
            End Using
        Catch ex As Exception
            MessageBox.Show("فشل الاتصال بقاعدة البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' إنشاء قاعدة البيانات إذا لم تكن موجودة
    Public Shared Function CreateDatabaseIfNotExists() As Boolean
        Try
            Dim masterConnectionString As String = "Data Source=.\SQLEXPRESS;Initial Catalog=master;Integrated Security=True"
            
            Using connection As New SqlConnection(masterConnectionString)
                connection.Open()
                
                ' التحقق من وجود قاعدة البيانات
                Dim checkQuery As String = "SELECT COUNT(*) FROM sys.databases WHERE name = 'CharitySystemDB'"
                Using command As New SqlCommand(checkQuery, connection)
                    Dim count As Integer = Convert.ToInt32(command.ExecuteScalar())
                    
                    If count = 0 Then
                        ' إنشاء قاعدة البيانات
                        Dim createQuery As String = "CREATE DATABASE CharitySystemDB"
                        Using createCommand As New SqlCommand(createQuery, connection)
                            createCommand.ExecuteNonQuery()
                        End Using
                        
                        MessageBox.Show("تم إنشاء قاعدة البيانات بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    End If
                End Using
            End Using
            
            Return True
        Catch ex As Exception
            MessageBox.Show("خطأ في إنشاء قاعدة البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function
End Class
