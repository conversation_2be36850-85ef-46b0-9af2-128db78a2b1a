Public Class BeneficiaryManager
    ' فئة المحتاج
    Public Class Beneficiary
        Public Property BeneficiaryID As Integer
        Public Property NationalID As String
        Public Property FullName As String
        Public Property Nationality As String
        Public Property Age As Integer
        Public Property PhoneNumber As String
        Public Property Address As String
        Public Property EmploymentStatus As String
        Public Property Workplace As String
        Public Property FamilyMembers As Integer
        Public Property NeedType As String
        Public Property HealthCondition As String
        Public Property MonthlyIncome As Decimal
        Public Property ApplicationStatus As String
        Public Property ApprovedBy As Integer?
        Public Property RegistrationDate As DateTime
        Public Property ApprovalDate As DateTime?
        Public Property Notes As String
    End Class

    ' تسجيل محتاج جديد
    Public Shared Function RegisterBeneficiary(beneficiary As Beneficiary) As Boolean
        Try
            ' التحقق من عدم وجود الرقم الوطني
            If IsNationalIDExists(beneficiary.NationalID) Then
                MessageBox.Show("الرقم الوطني موجود بالفعل في النظام!", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return False
            End If

            Dim parameters() As SqlParameter = {
                New SqlParameter("@NationalID", beneficiary.NationalID),
                New SqlParameter("@FullName", beneficiary.FullName),
                New SqlParameter("@Nationality", If(String.IsNullOrEmpty(beneficiary.Nationality), DBNull.Value, beneficiary.Nationality)),
                New SqlParameter("@Age", beneficiary.Age),
                New SqlParameter("@PhoneNumber", If(String.IsNullOrEmpty(beneficiary.PhoneNumber), DBNull.Value, beneficiary.PhoneNumber)),
                New SqlParameter("@Address", If(String.IsNullOrEmpty(beneficiary.Address), DBNull.Value, beneficiary.Address)),
                New SqlParameter("@EmploymentStatus", If(String.IsNullOrEmpty(beneficiary.EmploymentStatus), DBNull.Value, beneficiary.EmploymentStatus)),
                New SqlParameter("@Workplace", If(String.IsNullOrEmpty(beneficiary.Workplace), DBNull.Value, beneficiary.Workplace)),
                New SqlParameter("@FamilyMembers", beneficiary.FamilyMembers),
                New SqlParameter("@NeedType", If(String.IsNullOrEmpty(beneficiary.NeedType), DBNull.Value, beneficiary.NeedType)),
                New SqlParameter("@HealthCondition", If(String.IsNullOrEmpty(beneficiary.HealthCondition), DBNull.Value, beneficiary.HealthCondition)),
                New SqlParameter("@MonthlyIncome", beneficiary.MonthlyIncome)
            }

            Return DatabaseConnection.ExecuteStoredProcedureNonQuery("sp_RegisterBeneficiary", parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في تسجيل المحتاج: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' التحقق من وجود الرقم الوطني
    Private Shared Function IsNationalIDExists(nationalID As String) As Boolean
        Try
            Dim query As String = "SELECT COUNT(*) FROM Beneficiaries WHERE NationalID = @NationalID"
            Dim parameters() As SqlParameter = {New SqlParameter("@NationalID", nationalID)}

            Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query, parameters)
            If dataTable.Rows.Count > 0 Then
                Return Convert.ToInt32(dataTable.Rows(0)(0)) > 0
            End If

            Return False
        Catch ex As Exception
            MessageBox.Show("خطأ في التحقق من الرقم الوطني: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' الحصول على جميع المحتاجين
    Public Shared Function GetAllBeneficiaries(Optional status As String = Nothing) As DataTable
        Try
            Dim parameters() As SqlParameter = Nothing
            If Not String.IsNullOrEmpty(status) Then
                parameters = {New SqlParameter("@Status", status)}
            End If

            Return DatabaseConnection.ExecuteStoredProcedure("sp_GetBeneficiariesReport", parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في جلب بيانات المحتاجين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return New DataTable()
        End Try
    End Function

    ' الحصول على المحتاجين المعلقين (في انتظار الموافقة)
    Public Shared Function GetPendingBeneficiaries() As DataTable
        Return GetAllBeneficiaries("Pending")
    End Function

    ' الحصول على المحتاجين المعتمدين
    Public Shared Function GetApprovedBeneficiaries() As DataTable
        Return GetAllBeneficiaries("Approved")
    End Function

    ' الموافقة على طلب محتاج
    Public Shared Function ApproveBeneficiary(beneficiaryID As Integer, approvedBy As Integer, notes As String) As Boolean
        Try
            Dim query As String = "UPDATE Beneficiaries SET ApplicationStatus = 'Approved', " &
                                 "ApprovedBy = @ApprovedBy, ApprovalDate = GETDATE(), Notes = @Notes " &
                                 "WHERE BeneficiaryID = @BeneficiaryID"

            Dim parameters() As SqlParameter = {
                New SqlParameter("@BeneficiaryID", beneficiaryID),
                New SqlParameter("@ApprovedBy", approvedBy),
                New SqlParameter("@Notes", If(String.IsNullOrEmpty(notes), DBNull.Value, notes))
            }

            Return DatabaseConnection.ExecuteNonQuery(query, parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في الموافقة على الطلب: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' رفض طلب محتاج
    Public Shared Function RejectBeneficiary(beneficiaryID As Integer, rejectedBy As Integer, notes As String) As Boolean
        Try
            Dim query As String = "UPDATE Beneficiaries SET ApplicationStatus = 'Rejected', " &
                                 "ApprovedBy = @RejectedBy, ApprovalDate = GETDATE(), Notes = @Notes " &
                                 "WHERE BeneficiaryID = @BeneficiaryID"

            Dim parameters() As SqlParameter = {
                New SqlParameter("@BeneficiaryID", beneficiaryID),
                New SqlParameter("@RejectedBy", rejectedBy),
                New SqlParameter("@Notes", If(String.IsNullOrEmpty(notes), DBNull.Value, notes))
            }

            Return DatabaseConnection.ExecuteNonQuery(query, parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في رفض الطلب: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' تحديث بيانات محتاج
    Public Shared Function UpdateBeneficiary(beneficiary As Beneficiary) As Boolean
        Try
            Dim query As String = "UPDATE Beneficiaries SET FullName = @FullName, Nationality = @Nationality, " &
                                 "Age = @Age, PhoneNumber = @PhoneNumber, Address = @Address, " &
                                 "EmploymentStatus = @EmploymentStatus, Workplace = @Workplace, " &
                                 "FamilyMembers = @FamilyMembers, NeedType = @NeedType, " &
                                 "HealthCondition = @HealthCondition, MonthlyIncome = @MonthlyIncome " &
                                 "WHERE BeneficiaryID = @BeneficiaryID"

            Dim parameters() As SqlParameter = {
                New SqlParameter("@BeneficiaryID", beneficiary.BeneficiaryID),
                New SqlParameter("@FullName", beneficiary.FullName),
                New SqlParameter("@Nationality", If(String.IsNullOrEmpty(beneficiary.Nationality), DBNull.Value, beneficiary.Nationality)),
                New SqlParameter("@Age", beneficiary.Age),
                New SqlParameter("@PhoneNumber", If(String.IsNullOrEmpty(beneficiary.PhoneNumber), DBNull.Value, beneficiary.PhoneNumber)),
                New SqlParameter("@Address", If(String.IsNullOrEmpty(beneficiary.Address), DBNull.Value, beneficiary.Address)),
                New SqlParameter("@EmploymentStatus", If(String.IsNullOrEmpty(beneficiary.EmploymentStatus), DBNull.Value, beneficiary.EmploymentStatus)),
                New SqlParameter("@Workplace", If(String.IsNullOrEmpty(beneficiary.Workplace), DBNull.Value, beneficiary.Workplace)),
                New SqlParameter("@FamilyMembers", beneficiary.FamilyMembers),
                New SqlParameter("@NeedType", If(String.IsNullOrEmpty(beneficiary.NeedType), DBNull.Value, beneficiary.NeedType)),
                New SqlParameter("@HealthCondition", If(String.IsNullOrEmpty(beneficiary.HealthCondition), DBNull.Value, beneficiary.HealthCondition)),
                New SqlParameter("@MonthlyIncome", beneficiary.MonthlyIncome)
            }

            Return DatabaseConnection.ExecuteNonQuery(query, parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث بيانات المحتاج: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' البحث عن محتاج بالرقم الوطني
    Public Shared Function SearchByNationalID(nationalID As String) As Beneficiary
        Try
            Dim query As String = "SELECT * FROM Beneficiaries WHERE NationalID = @NationalID"
            Dim parameters() As SqlParameter = {New SqlParameter("@NationalID", nationalID)}

            Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query, parameters)
            If dataTable.Rows.Count > 0 Then
                Dim row As DataRow = dataTable.Rows(0)
                Return CreateBeneficiaryFromDataRow(row)
            End If

            Return Nothing
        Catch ex As Exception
            MessageBox.Show("خطأ في البحث: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return Nothing
        End Try
    End Function

    ' إنشاء كائن محتاج من صف البيانات
    Private Shared Function CreateBeneficiaryFromDataRow(row As DataRow) As Beneficiary
        Return New Beneficiary() With {
            .BeneficiaryID = Convert.ToInt32(row("BeneficiaryID")),
            .NationalID = row("NationalID").ToString(),
            .FullName = row("FullName").ToString(),
            .Nationality = If(row("Nationality") Is DBNull.Value, "", row("Nationality").ToString()),
            .Age = If(row("Age") Is DBNull.Value, 0, Convert.ToInt32(row("Age"))),
            .PhoneNumber = If(row("PhoneNumber") Is DBNull.Value, "", row("PhoneNumber").ToString()),
            .Address = If(row("Address") Is DBNull.Value, "", row("Address").ToString()),
            .EmploymentStatus = If(row("EmploymentStatus") Is DBNull.Value, "", row("EmploymentStatus").ToString()),
            .Workplace = If(row("Workplace") Is DBNull.Value, "", row("Workplace").ToString()),
            .FamilyMembers = If(row("FamilyMembers") Is DBNull.Value, 0, Convert.ToInt32(row("FamilyMembers"))),
            .NeedType = If(row("NeedType") Is DBNull.Value, "", row("NeedType").ToString()),
            .HealthCondition = If(row("HealthCondition") Is DBNull.Value, "", row("HealthCondition").ToString()),
            .MonthlyIncome = If(row("MonthlyIncome") Is DBNull.Value, 0, Convert.ToDecimal(row("MonthlyIncome"))),
            .ApplicationStatus = row("ApplicationStatus").ToString(),
            .ApprovedBy = If(row("ApprovedBy") Is DBNull.Value, Nothing, Convert.ToInt32(row("ApprovedBy"))),
            .RegistrationDate = Convert.ToDateTime(row("RegistrationDate")),
            .ApprovalDate = If(row("ApprovalDate") Is DBNull.Value, Nothing, Convert.ToDateTime(row("ApprovalDate"))),
            .Notes = If(row("Notes") Is DBNull.Value, "", row("Notes").ToString())
        }
    End Function

    ' الحصول على إحصائيات المحتاجين
    Public Shared Function GetBeneficiariesStatistics() As Dictionary(Of String, Integer)
        Try
            Dim stats As New Dictionary(Of String, Integer)

            Dim query As String = "SELECT ApplicationStatus, COUNT(*) as Count FROM Beneficiaries GROUP BY ApplicationStatus"
            Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query)

            For Each row As DataRow In dataTable.Rows
                stats(row("ApplicationStatus").ToString()) = Convert.ToInt32(row("Count"))
            Next

            Return stats
        Catch ex As Exception
            MessageBox.Show("خطأ في جلب الإحصائيات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return New Dictionary(Of String, Integer)
        End Try
    End Function
End Class
