﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
    </startup>

    <connectionStrings>
        <add name="CharitySystemDB"
             connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=CharitySystemDB;Integrated Security=True"
             providerName="System.Data.SqlClient" />
    </connectionStrings>

    <appSettings>
        <!-- إعدادات النظام -->
        <add key="SystemName" value="نظام إدارة التبرعات والمحتاجين" />
        <add key="Version" value="1.0.0" />
        <add key="Developer" value="فريق التطوير" />

        <!-- إعدادات قاعدة البيانات -->
        <add key="DatabaseTimeout" value="30" />
        <add key="EnableDatabaseLogging" value="true" />

        <!-- إعدادات التقارير -->
        <add key="ReportsPath" value="Reports\" />
        <add key="ExportsPath" value="Exports\" />

        <!-- إعدادات الأمان -->
        <add key="PasswordMinLength" value="6" />
        <add key="SessionTimeout" value="60" />

        <!-- إعدادات النسخ الاحتياطي -->
        <add key="BackupPath" value="Backup\" />
        <add key="AutoBackup" value="true" />
        <add key="BackupInterval" value="24" />
    </appSettings>
</configuration>