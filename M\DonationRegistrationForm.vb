Public Class DonationRegistrationForm
    Private currentUser As UserManager.User

    Public Sub New(user As UserManager.User)
        InitializeComponent()
        currentUser = user
    End Sub

    Private Sub DonationRegistrationForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة النموذج
        Me.Text = "تسجيل تبرع جديد"
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized

        ' تهيئة القوائم المنسدلة
        InitializeComboBoxes()
        
        ' تعيين التاريخ الحالي
        dtpDonationDate.Value = DateTime.Now
        
        ' تعيين التركيز على أول حقل
        txtDonorName.Focus()
    End Sub

    Private Sub InitializeComboBoxes()
        ' قائمة أنواع التبرعات
        cmbDonationType.Items.AddRange({"مالي", "مواد غذائية", "ملابس", "مستلزمات طبية", "أخرى"})
        
        ' قائمة طرق التبرع
        cmbDonationMethod.Items.AddRange({"نقدي", "تحويل بنكي", "توصيل"})
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        ' التحقق من صحة البيانات
        If Not ValidateInput() Then
            Return
        End If

        ' حفظ التبرع
        Dim success As Boolean = DonationManager.RegisterDonation(
            txtDonorName.Text.Trim(),
            txtDonorPhone.Text.Trim(),
            txtDonorNationalID.Text.Trim(),
            GetDonationTypeInEnglish(cmbDonationType.Text),
            GetDonationMethodInEnglish(cmbDonationMethod.Text),
            numAmount.Value,
            txtQuantity.Text.Trim(),
            txtDescription.Text.Trim(),
            currentUser.UserID
        )

        If success Then
            MessageBox.Show("تم تسجيل التبرع بنجاح!", "نجح التسجيل", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ClearForm()
        End If
    End Sub

    Private Function ValidateInput() As Boolean
        ' التحقق من اسم المتبرع
        If String.IsNullOrWhiteSpace(txtDonorName.Text) Then
            MessageBox.Show("يرجى إدخال اسم المتبرع", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtDonorName.Focus()
            Return False
        End If

        ' التحقق من نوع التبرع
        If cmbDonationType.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار نوع التبرع", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbDonationType.Focus()
            Return False
        End If

        ' التحقق من المبلغ أو الكمية
        If cmbDonationType.Text = "مالي" Then
            If numAmount.Value <= 0 Then
                MessageBox.Show("يرجى إدخال مبلغ التبرع", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                numAmount.Focus()
                Return False
            End If
        Else
            If String.IsNullOrWhiteSpace(txtQuantity.Text) Then
                MessageBox.Show("يرجى إدخال كمية التبرع", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtQuantity.Focus()
                Return False
            End If
        End If

        Return True
    End Function

    Private Function GetDonationTypeInEnglish(arabicType As String) As String
        Select Case arabicType
            Case "مالي"
                Return "Money"
            Case "مواد غذائية"
                Return "Food"
            Case "ملابس"
                Return "Clothes"
            Case "مستلزمات طبية"
                Return "Medical"
            Case Else
                Return "Other"
        End Select
    End Function

    Private Function GetDonationMethodInEnglish(arabicMethod As String) As String
        Select Case arabicMethod
            Case "نقدي"
                Return "Cash"
            Case "تحويل بنكي"
                Return "Transfer"
            Case "توصيل"
                Return "Delivery"
            Case Else
                Return "Cash"
        End Select
    End Function

    Private Sub ClearForm()
        ' مسح جميع الحقول
        txtDonorName.Clear()
        txtDonorPhone.Clear()
        txtDonorNationalID.Clear()
        cmbDonationType.SelectedIndex = -1
        cmbDonationMethod.SelectedIndex = -1
        numAmount.Value = 0
        txtQuantity.Clear()
        txtDescription.Clear()
        dtpDonationDate.Value = DateTime.Now

        ' تعيين التركيز على أول حقل
        txtDonorName.Focus()
    End Sub

    Private Sub btnClear_Click(sender As Object, e As EventArgs) Handles btnClear.Click
        If MessageBox.Show("هل تريد مسح جميع البيانات؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            ClearForm()
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub cmbDonationType_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbDonationType.SelectedIndexChanged
        ' إظهار/إخفاء حقول المبلغ والكمية حسب نوع التبرع
        If cmbDonationType.Text = "مالي" Then
            lblAmount.Visible = True
            numAmount.Visible = True
            lblQuantity.Visible = False
            txtQuantity.Visible = False
        Else
            lblAmount.Visible = False
            numAmount.Visible = False
            lblQuantity.Visible = True
            txtQuantity.Visible = True
        End If
    End Sub

    Private Sub txtDonorPhone_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtDonorPhone.KeyPress
        ' السماح بالأرقام والرموز فقط
        If Not Char.IsDigit(e.KeyChar) AndAlso Not Char.IsControl(e.KeyChar) AndAlso e.KeyChar <> "+"c AndAlso e.KeyChar <> "-"c Then
            e.Handled = True
        End If
    End Sub

    Private Sub txtDonorNationalID_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtDonorNationalID.KeyPress
        ' السماح بالأرقام فقط
        If Not Char.IsDigit(e.KeyChar) AndAlso Not Char.IsControl(e.KeyChar) Then
            e.Handled = True
        End If
    End Sub

    Private Sub btnSearchDonor_Click(sender As Object, e As EventArgs) Handles btnSearchDonor.Click
        ' البحث عن متبرع موجود
        If String.IsNullOrWhiteSpace(txtDonorNationalID.Text) Then
            MessageBox.Show("يرجى إدخال الرقم الوطني للبحث", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtDonorNationalID.Focus()
            Return
        End If

        Dim donor As DonationManager.Donor = DonationManager.SearchDonorByNationalID(txtDonorNationalID.Text.Trim())
        
        If donor IsNot Nothing Then
            ' ملء البيانات الموجودة
            txtDonorName.Text = donor.DonorName
            txtDonorPhone.Text = donor.PhoneNumber
            MessageBox.Show("تم العثور على المتبرع وتم ملء البيانات", "متبرع موجود", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            MessageBox.Show("لم يتم العثور على متبرع بهذا الرقم الوطني", "غير موجود", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
End Class
