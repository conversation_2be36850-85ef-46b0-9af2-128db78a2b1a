Public Class DonationManager
    ' فئة المتبرع
    Public Class Donor
        Public Property DonorID As Integer
        Public Property DonorName As String
        Public Property PhoneNumber As String
        Public Property NationalID As String
        Public Property RegistrationDate As DateTime
    End Class

    ' فئة التبرع
    Public Class Donation
        Public Property DonationID As Integer
        Public Property DonorID As Integer
        Public Property DonorName As String
        Public Property DonationType As String
        Public Property DonationMethod As String
        Public Property Amount As Decimal
        Public Property Quantity As String
        Public Property Description As String
        Public Property DonationDate As DateTime
        Public Property Status As String
        Public Property ReceivedBy As Integer
        Public Property ReceivedByName As String
    End Class

    ' تسجيل تبرع جديد
    Public Shared Function RegisterDonation(donorName As String, donorPhone As String, donorNationalID As String,
                                          donationType As String, donationMethod As String, amount As Decimal,
                                          quantity As String, description As String, receivedBy As Integer) As Boolean
        Try
            Dim parameters() As SqlParameter = {
                New SqlParameter("@DonorName", donorName),
                New SqlParameter("@DonorPhone", If(String.IsNullOrEmpty(donorPhone), DBNull.Value, donorPhone)),
                New SqlParameter("@DonorNationalID", If(String.IsNullOrEmpty(donorNationalID), DBNull.Value, donorNationalID)),
                New SqlParameter("@DonationType", donationType),
                New SqlParameter("@DonationMethod", If(String.IsNullOrEmpty(donationMethod), DBNull.Value, donationMethod)),
                New SqlParameter("@Amount", If(amount = 0, DBNull.Value, amount)),
                New SqlParameter("@Quantity", If(String.IsNullOrEmpty(quantity), DBNull.Value, quantity)),
                New SqlParameter("@Description", If(String.IsNullOrEmpty(description), DBNull.Value, description)),
                New SqlParameter("@ReceivedBy", receivedBy)
            }

            Return DatabaseConnection.ExecuteStoredProcedureNonQuery("sp_RegisterDonation", parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في تسجيل التبرع: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' الحصول على جميع التبرعات
    Public Shared Function GetAllDonations(Optional startDate As DateTime? = Nothing,
                                         Optional endDate As DateTime? = Nothing,
                                         Optional donationType As String = Nothing) As DataTable
        Try
            Dim parameters As New List(Of SqlParameter)

            If startDate.HasValue Then
                parameters.Add(New SqlParameter("@StartDate", startDate.Value))
            End If

            If endDate.HasValue Then
                parameters.Add(New SqlParameter("@EndDate", endDate.Value))
            End If

            If Not String.IsNullOrEmpty(donationType) Then
                parameters.Add(New SqlParameter("@DonationType", donationType))
            End If

            Return DatabaseConnection.ExecuteStoredProcedure("sp_GetDonationsReport", parameters.ToArray())
        Catch ex As Exception
            MessageBox.Show("خطأ في جلب بيانات التبرعات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return New DataTable()
        End Try
    End Function

    ' الحصول على التبرعات حسب النوع
    Public Shared Function GetDonationsByType(donationType As String) As DataTable
        Return GetAllDonations(donationType:=donationType)
    End Function

    ' الحصول على التبرعات في فترة زمنية
    Public Shared Function GetDonationsByDateRange(startDate As DateTime, endDate As DateTime) As DataTable
        Return GetAllDonations(startDate, endDate)
    End Function

    ' تحديث حالة التبرع
    Public Shared Function UpdateDonationStatus(donationID As Integer, status As String) As Boolean
        Try
            Dim query As String = "UPDATE Donations SET Status = @Status WHERE DonationID = @DonationID"
            Dim parameters() As SqlParameter = {
                New SqlParameter("@DonationID", donationID),
                New SqlParameter("@Status", status)
            }

            Return DatabaseConnection.ExecuteNonQuery(query, parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث حالة التبرع: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' توزيع التبرع على محتاج
    Public Shared Function DistributeDonation(donationID As Integer, beneficiaryID As Integer,
                                            distributedAmount As Decimal, distributedQuantity As String,
                                            distributedBy As Integer, notes As String) As Boolean
        Try
            ' إدراج سجل التوزيع
            Dim insertQuery As String = "INSERT INTO DonationDistribution (DonationID, BeneficiaryID, DistributedAmount, " &
                                       "DistributedQuantity, DistributedBy, Notes) " &
                                       "VALUES (@DonationID, @BeneficiaryID, @DistributedAmount, @DistributedQuantity, " &
                                       "@DistributedBy, @Notes)"

            Dim insertParameters() As SqlParameter = {
                New SqlParameter("@DonationID", donationID),
                New SqlParameter("@BeneficiaryID", beneficiaryID),
                New SqlParameter("@DistributedAmount", If(distributedAmount = 0, DBNull.Value, distributedAmount)),
                New SqlParameter("@DistributedQuantity", If(String.IsNullOrEmpty(distributedQuantity), DBNull.Value, distributedQuantity)),
                New SqlParameter("@DistributedBy", distributedBy),
                New SqlParameter("@Notes", If(String.IsNullOrEmpty(notes), DBNull.Value, notes))
            }

            If DatabaseConnection.ExecuteNonQuery(insertQuery, insertParameters) Then
                ' تحديث حالة التبرع إلى "موزع"
                Return UpdateDonationStatus(donationID, "Distributed")
            End If

            Return False
        Catch ex As Exception
            MessageBox.Show("خطأ في توزيع التبرع: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' الحصول على سجل التوزيعات
    Public Shared Function GetDistributionHistory() As DataTable
        Try
            Dim query As String = "SELECT dd.DistributionID, d.DonationType, don.DonorName, b.FullName as BeneficiaryName, " &
                                 "dd.DistributedAmount, dd.DistributedQuantity, dd.DistributionDate, u.FullName as DistributedByName, " &
                                 "dd.Notes " &
                                 "FROM DonationDistribution dd " &
                                 "INNER JOIN Donations d ON dd.DonationID = d.DonationID " &
                                 "INNER JOIN Donors don ON d.DonorID = don.DonorID " &
                                 "INNER JOIN Beneficiaries b ON dd.BeneficiaryID = b.BeneficiaryID " &
                                 "LEFT JOIN Users u ON dd.DistributedBy = u.UserID " &
                                 "ORDER BY dd.DistributionDate DESC"

            Return DatabaseConnection.ExecuteQuery(query)
        Catch ex As Exception
            MessageBox.Show("خطأ في جلب سجل التوزيعات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return New DataTable()
        End Try
    End Function

    ' الحصول على إحصائيات التبرعات
    Public Shared Function GetDonationsStatistics() As Dictionary(Of String, Object)
        Try
            Dim stats As New Dictionary(Of String, Object)

            ' إجمالي التبرعات المالية
            Dim moneyQuery As String = "SELECT ISNULL(SUM(Amount), 0) FROM Donations WHERE DonationType = 'Money'"
            Dim moneyData As DataTable = DatabaseConnection.ExecuteQuery(moneyQuery)
            If moneyData.Rows.Count > 0 Then
                stats("TotalMoney") = Convert.ToDecimal(moneyData.Rows(0)(0))
            End If

            ' عدد التبرعات حسب النوع
            Dim typeQuery As String = "SELECT DonationType, COUNT(*) as Count FROM Donations GROUP BY DonationType"
            Dim typeData As DataTable = DatabaseConnection.ExecuteQuery(typeQuery)
            For Each row As DataRow In typeData.Rows
                stats("Count_" & row("DonationType").ToString()) = Convert.ToInt32(row("Count"))
            Next

            ' عدد التبرعات حسب الحالة
            Dim statusQuery As String = "SELECT Status, COUNT(*) as Count FROM Donations GROUP BY Status"
            Dim statusData As DataTable = DatabaseConnection.ExecuteQuery(statusQuery)
            For Each row As DataRow In statusData.Rows
                stats("Status_" & row("Status").ToString()) = Convert.ToInt32(row("Count"))
            Next

            ' عدد المتبرعين
            Dim donorsQuery As String = "SELECT COUNT(*) FROM Donors"
            Dim donorsData As DataTable = DatabaseConnection.ExecuteQuery(donorsQuery)
            If donorsData.Rows.Count > 0 Then
                stats("TotalDonors") = Convert.ToInt32(donorsData.Rows(0)(0))
            End If

            Return stats
        Catch ex As Exception
            MessageBox.Show("خطأ في جلب إحصائيات التبرعات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return New Dictionary(Of String, Object)
        End Try
    End Function

    ' البحث عن متبرع بالرقم الوطني
    Public Shared Function SearchDonorByNationalID(nationalID As String) As Donor
        Try
            Dim query As String = "SELECT * FROM Donors WHERE NationalID = @NationalID"
            Dim parameters() As SqlParameter = {New SqlParameter("@NationalID", nationalID)}

            Dim dataTable As DataTable = DatabaseConnection.ExecuteQuery(query, parameters)
            If dataTable.Rows.Count > 0 Then
                Dim row As DataRow = dataTable.Rows(0)
                Return New Donor() With {
                    .DonorID = Convert.ToInt32(row("DonorID")),
                    .DonorName = row("DonorName").ToString(),
                    .PhoneNumber = If(row("PhoneNumber") Is DBNull.Value, "", row("PhoneNumber").ToString()),
                    .NationalID = If(row("NationalID") Is DBNull.Value, "", row("NationalID").ToString()),
                    .RegistrationDate = Convert.ToDateTime(row("RegistrationDate"))
                }
            End If

            Return Nothing
        Catch ex As Exception
            MessageBox.Show("خطأ في البحث عن المتبرع: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return Nothing
        End Try
    End Function

    ' الحصول على جميع المتبرعين
    Public Shared Function GetAllDonors() As DataTable
        Try
            Dim query As String = "SELECT d.DonorID, d.DonorName, d.PhoneNumber, d.NationalID, d.RegistrationDate, " &
                                 "COUNT(don.DonationID) as TotalDonations, " &
                                 "ISNULL(SUM(CASE WHEN don.DonationType = 'Money' THEN don.Amount ELSE 0 END), 0) as TotalAmount " &
                                 "FROM Donors d " &
                                 "LEFT JOIN Donations don ON d.DonorID = don.DonorID " &
                                 "GROUP BY d.DonorID, d.DonorName, d.PhoneNumber, d.NationalID, d.RegistrationDate " &
                                 "ORDER BY d.RegistrationDate DESC"

            Return DatabaseConnection.ExecuteQuery(query)
        Catch ex As Exception
            MessageBox.Show("خطأ في جلب بيانات المتبرعين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return New DataTable()
        End Try
    End Function

    ' الحصول على تبرعات متبرع معين
    Public Shared Function GetDonorDonations(donorID As Integer) As DataTable
        Try
            Dim query As String = "SELECT d.DonationID, d.DonationType, d.DonationMethod, d.Amount, d.Quantity, " &
                                 "d.Description, d.DonationDate, d.Status, u.FullName as ReceivedByName " &
                                 "FROM Donations d " &
                                 "LEFT JOIN Users u ON d.ReceivedBy = u.UserID " &
                                 "WHERE d.DonorID = @DonorID " &
                                 "ORDER BY d.DonationDate DESC"

            Dim parameters() As SqlParameter = {New SqlParameter("@DonorID", donorID)}
            Return DatabaseConnection.ExecuteQuery(query, parameters)
        Catch ex As Exception
            MessageBox.Show("خطأ في جلب تبرعات المتبرع: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return New DataTable()
        End Try
    End Function
End Class
