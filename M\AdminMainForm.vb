Public Class AdminMainForm
    Private currentUser As UserManager.User

    Public Sub New(user As UserManager.User)
        InitializeComponent()
        currentUser = user
    End Sub

    Private Sub AdminMainForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة النموذج
        Me.Text = "نظام إدارة التبرعات والمحتاجين - لوحة تحكم المدير"
        Me.WindowState = FormWindowState.Maximized
        
        ' عرض معلومات المستخدم
        lblWelcome.Text = $"مرحباً {currentUser.FullName}"
        lblUserRole.Text = "مدير النظام"
        
        ' تحديث الإحصائيات
        UpdateStatistics()
        
        ' تحديث قوائم البيانات
        LoadPendingBeneficiaries()
        LoadRecentDonations()
    End Sub

    Private Sub UpdateStatistics()
        Try
            ' إحصائيات المحتاجين
            Dim beneficiaryStats = BeneficiaryManager.GetBeneficiariesStatistics()
            lblTotalBeneficiaries.Text = (beneficiaryStats.Values.Sum()).ToString()
            lblPendingBeneficiaries.Text = If(beneficiaryStats.ContainsKey("Pending"), beneficiaryStats("Pending"), 0).ToString()
            lblApprovedBeneficiaries.Text = If(beneficiaryStats.ContainsKey("Approved"), beneficiaryStats("Approved"), 0).ToString()

            ' إحصائيات التبرعات
            Dim donationStats = DonationManager.GetDonationsStatistics()
            lblTotalDonations.Text = (donationStats.Where(Function(x) x.Key.StartsWith("Count_")).Sum(Function(x) CInt(x.Value))).ToString()
            lblTotalMoney.Text = If(donationStats.ContainsKey("TotalMoney"), CDec(donationStats("TotalMoney")).ToString("C"), "0").Replace("$", "ريال")
            lblTotalDonors.Text = If(donationStats.ContainsKey("TotalDonors"), donationStats("TotalDonors"), 0).ToString()

        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث الإحصائيات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadPendingBeneficiaries()
        Try
            dgvPendingBeneficiaries.DataSource = BeneficiaryManager.GetPendingBeneficiaries()
            dgvPendingBeneficiaries.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل المحتاجين المعلقين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadRecentDonations()
        Try
            dgvRecentDonations.DataSource = DonationManager.GetAllDonations()
            dgvRecentDonations.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل التبرعات الحديثة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' أزرار القائمة الجانبية
    Private Sub btnRegisterBeneficiary_Click(sender As Object, e As EventArgs) Handles btnRegisterBeneficiary.Click
        Dim form As New BeneficiaryRegistrationForm(currentUser)
        form.ShowDialog()
        LoadPendingBeneficiaries()
        UpdateStatistics()
    End Sub

    Private Sub btnRegisterDonation_Click(sender As Object, e As EventArgs) Handles btnRegisterDonation.Click
        Dim form As New DonationRegistrationForm(currentUser)
        form.ShowDialog()
        LoadRecentDonations()
        UpdateStatistics()
    End Sub

    Private Sub btnManageBeneficiaries_Click(sender As Object, e As EventArgs) Handles btnManageBeneficiaries.Click
        Dim form As New BeneficiaryManagementForm(currentUser)
        form.ShowDialog()
        LoadPendingBeneficiaries()
        UpdateStatistics()
    End Sub

    Private Sub btnManageDonations_Click(sender As Object, e As EventArgs) Handles btnManageDonations.Click
        Dim form As New DonationManagementForm(currentUser)
        form.ShowDialog()
        LoadRecentDonations()
        UpdateStatistics()
    End Sub

    Private Sub btnManageUsers_Click(sender As Object, e As EventArgs) Handles btnManageUsers.Click
        Dim form As New UserManagementForm(currentUser)
        form.ShowDialog()
    End Sub

    Private Sub btnReports_Click(sender As Object, e As EventArgs) Handles btnReports.Click
        Dim form As New ReportsForm(currentUser)
        form.ShowDialog()
    End Sub

    Private Sub btnSettings_Click(sender As Object, e As EventArgs) Handles btnSettings.Click
        Dim form As New SettingsForm(currentUser)
        form.ShowDialog()
    End Sub

    Private Sub btnLogout_Click(sender As Object, e As EventArgs) Handles btnLogout.Click
        If MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Me.Hide()
            Dim loginForm As New LoginForm()
            loginForm.Show()
        End If
    End Sub

    ' أزرار الموافقة والرفض للمحتاجين المعلقين
    Private Sub btnApproveBeneficiary_Click(sender As Object, e As EventArgs) Handles btnApproveBeneficiary.Click
        If dgvPendingBeneficiaries.SelectedRows.Count = 0 Then
            MessageBox.Show("يرجى اختيار محتاج للموافقة عليه", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim beneficiaryID As Integer = Convert.ToInt32(dgvPendingBeneficiaries.SelectedRows(0).Cells("BeneficiaryID").Value)
        Dim beneficiaryName As String = dgvPendingBeneficiaries.SelectedRows(0).Cells("FullName").Value.ToString()

        If MessageBox.Show($"هل تريد الموافقة على طلب {beneficiaryName}؟", "تأكيد الموافقة", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            If BeneficiaryManager.ApproveBeneficiary(beneficiaryID, currentUser.UserID, "تمت الموافقة من قبل المدير") Then
                MessageBox.Show("تمت الموافقة على الطلب بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadPendingBeneficiaries()
                UpdateStatistics()
            End If
        End If
    End Sub

    Private Sub btnRejectBeneficiary_Click(sender As Object, e As EventArgs) Handles btnRejectBeneficiary.Click
        If dgvPendingBeneficiaries.SelectedRows.Count = 0 Then
            MessageBox.Show("يرجى اختيار محتاج لرفض طلبه", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim beneficiaryID As Integer = Convert.ToInt32(dgvPendingBeneficiaries.SelectedRows(0).Cells("BeneficiaryID").Value)
        Dim beneficiaryName As String = dgvPendingBeneficiaries.SelectedRows(0).Cells("FullName").Value.ToString()

        Dim reason As String = InputBox("يرجى إدخال سبب الرفض:", "سبب الرفض")
        If String.IsNullOrWhiteSpace(reason) Then
            Return
        End If

        If MessageBox.Show($"هل تريد رفض طلب {beneficiaryName}؟", "تأكيد الرفض", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            If BeneficiaryManager.RejectBeneficiary(beneficiaryID, currentUser.UserID, reason) Then
                MessageBox.Show("تم رفض الطلب!", "تم الرفض", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadPendingBeneficiaries()
                UpdateStatistics()
            End If
        End If
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        UpdateStatistics()
        LoadPendingBeneficiaries()
        LoadRecentDonations()
    End Sub

    Private Sub AdminMainForm_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Application.Exit()
    End Sub

    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        ' تحديث الوقت
        lblCurrentTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")
    End Sub
End Class
