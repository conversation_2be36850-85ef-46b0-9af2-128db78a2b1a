-- نظام إدارة التبرعات والمحتاجين
-- Charity Management System Database

USE master;
GO

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'CharitySystemDB')
BEGIN
    CREATE DATABASE CharitySystemDB;
END
GO

USE CharitySystemDB;
GO

-- جدول المستخدمين (المدير والموظفين)
CREATE TABLE Users (
    UserID INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) UNIQUE NOT NULL,
    Password NVARCHAR(255) NOT NULL,
    FullName NVARCHAR(100) NOT NULL,
    PhoneNumber NVARCHAR(20),
    UserRole NVARCHAR(20) NOT NULL CHECK (UserRole IN ('Admin', 'Employee')),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    LastLogin DATETIME
);

-- جدول المحتاجين
CREATE TABLE Beneficiaries (
    BeneficiaryID INT IDENTITY(1,1) PRIMARY KEY,
    NationalID NVARCHAR(20) UNIQUE NOT NULL,
    FullName NVARCHAR(100) NOT NULL,
    Nationality NVARCHAR(50),
    Age INT,
    PhoneNumber NVARCHAR(20),
    Address NVARCHAR(200),
    EmploymentStatus NVARCHAR(50),
    Workplace NVARCHAR(100),
    FamilyMembers INT,
    NeedType NVARCHAR(100),
    HealthCondition NVARCHAR(200),
    MonthlyIncome DECIMAL(10,2),
    ApplicationStatus NVARCHAR(20) DEFAULT 'Pending' CHECK (ApplicationStatus IN ('Pending', 'Approved', 'Rejected')),
    ApprovedBy INT FOREIGN KEY REFERENCES Users(UserID),
    RegistrationDate DATETIME DEFAULT GETDATE(),
    ApprovalDate DATETIME,
    Notes NVARCHAR(500)
);

-- جدول المتبرعين
CREATE TABLE Donors (
    DonorID INT IDENTITY(1,1) PRIMARY KEY,
    DonorName NVARCHAR(100) NOT NULL,
    PhoneNumber NVARCHAR(20),
    NationalID NVARCHAR(20),
    RegistrationDate DATETIME DEFAULT GETDATE()
);

-- جدول التبرعات
CREATE TABLE Donations (
    DonationID INT IDENTITY(1,1) PRIMARY KEY,
    DonorID INT FOREIGN KEY REFERENCES Donors(DonorID),
    DonationType NVARCHAR(50) NOT NULL CHECK (DonationType IN ('Money', 'Food', 'Clothes', 'Medical', 'Other')),
    DonationMethod NVARCHAR(50) CHECK (DonationMethod IN ('Cash', 'Transfer', 'Delivery')),
    Amount DECIMAL(10,2),
    Quantity NVARCHAR(100),
    Description NVARCHAR(200),
    DonationDate DATETIME DEFAULT GETDATE(),
    Status NVARCHAR(20) DEFAULT 'Received' CHECK (Status IN ('Received', 'Distributed', 'Pending')),
    ReceivedBy INT FOREIGN KEY REFERENCES Users(UserID)
);

-- جدول توزيع التبرعات
CREATE TABLE DonationDistribution (
    DistributionID INT IDENTITY(1,1) PRIMARY KEY,
    DonationID INT FOREIGN KEY REFERENCES Donations(DonationID),
    BeneficiaryID INT FOREIGN KEY REFERENCES Beneficiaries(BeneficiaryID),
    DistributedAmount DECIMAL(10,2),
    DistributedQuantity NVARCHAR(100),
    DistributionDate DATETIME DEFAULT GETDATE(),
    DistributedBy INT FOREIGN KEY REFERENCES Users(UserID),
    Notes NVARCHAR(200)
);

-- جدول السجلات الطبية للمحتاجين
CREATE TABLE MedicalRecords (
    RecordID INT IDENTITY(1,1) PRIMARY KEY,
    BeneficiaryID INT FOREIGN KEY REFERENCES Beneficiaries(BeneficiaryID),
    DiseaseType NVARCHAR(100),
    TreatmentNeeded NVARCHAR(200),
    MedicationRequired NVARCHAR(200),
    RecordDate DATETIME DEFAULT GETDATE(),
    UpdatedBy INT FOREIGN KEY REFERENCES Users(UserID)
);

-- جدول الأصناف والمواد
CREATE TABLE Items (
    ItemID INT IDENTITY(1,1) PRIMARY KEY,
    ItemName NVARCHAR(100) NOT NULL,
    Category NVARCHAR(50),
    CurrentStock INT DEFAULT 0,
    MinimumStock INT DEFAULT 0,
    Unit NVARCHAR(20),
    ExpiryDate DATE,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول حركة المخزون
CREATE TABLE StockMovements (
    MovementID INT IDENTITY(1,1) PRIMARY KEY,
    ItemID INT FOREIGN KEY REFERENCES Items(ItemID),
    MovementType NVARCHAR(20) CHECK (MovementType IN ('In', 'Out')),
    Quantity INT NOT NULL,
    DonationID INT FOREIGN KEY REFERENCES Donations(DonationID),
    BeneficiaryID INT FOREIGN KEY REFERENCES Beneficiaries(BeneficiaryID),
    MovementDate DATETIME DEFAULT GETDATE(),
    ProcessedBy INT FOREIGN KEY REFERENCES Users(UserID),
    Notes NVARCHAR(200)
);

-- إدراج بيانات المدير الافتراضي
INSERT INTO Users (Username, Password, FullName, UserRole)
VALUES ('admin', 'admin123', 'مدير النظام', 'Admin');

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IX_Beneficiaries_NationalID ON Beneficiaries(NationalID);
CREATE INDEX IX_Beneficiaries_Status ON Beneficiaries(ApplicationStatus);
CREATE INDEX IX_Donations_Date ON Donations(DonationDate);
CREATE INDEX IX_Donations_Type ON Donations(DonationType);
CREATE INDEX IX_Users_Username ON Users(Username);

GO

-- إنشاء الإجراءات المخزنة

-- إجراء لتسجيل محتاج جديد
CREATE PROCEDURE sp_RegisterBeneficiary
    @NationalID NVARCHAR(20),
    @FullName NVARCHAR(100),
    @Nationality NVARCHAR(50),
    @Age INT,
    @PhoneNumber NVARCHAR(20),
    @Address NVARCHAR(200),
    @EmploymentStatus NVARCHAR(50),
    @Workplace NVARCHAR(100),
    @FamilyMembers INT,
    @NeedType NVARCHAR(100),
    @HealthCondition NVARCHAR(200),
    @MonthlyIncome DECIMAL(10,2)
AS
BEGIN
    INSERT INTO Beneficiaries (NationalID, FullName, Nationality, Age, PhoneNumber, Address, 
                              EmploymentStatus, Workplace, FamilyMembers, NeedType, 
                              HealthCondition, MonthlyIncome)
    VALUES (@NationalID, @FullName, @Nationality, @Age, @PhoneNumber, @Address,
            @EmploymentStatus, @Workplace, @FamilyMembers, @NeedType,
            @HealthCondition, @MonthlyIncome);
END
GO

-- إجراء لتسجيل تبرع جديد
CREATE PROCEDURE sp_RegisterDonation
    @DonorName NVARCHAR(100),
    @DonorPhone NVARCHAR(20),
    @DonorNationalID NVARCHAR(20),
    @DonationType NVARCHAR(50),
    @DonationMethod NVARCHAR(50),
    @Amount DECIMAL(10,2),
    @Quantity NVARCHAR(100),
    @Description NVARCHAR(200),
    @ReceivedBy INT
AS
BEGIN
    DECLARE @DonorID INT;
    
    -- البحث عن المتبرع أو إنشاء جديد
    SELECT @DonorID = DonorID FROM Donors WHERE NationalID = @DonorNationalID;
    
    IF @DonorID IS NULL
    BEGIN
        INSERT INTO Donors (DonorName, PhoneNumber, NationalID)
        VALUES (@DonorName, @DonorPhone, @DonorNationalID);
        SET @DonorID = SCOPE_IDENTITY();
    END
    
    -- تسجيل التبرع
    INSERT INTO Donations (DonorID, DonationType, DonationMethod, Amount, Quantity, Description, ReceivedBy)
    VALUES (@DonorID, @DonationType, @DonationMethod, @Amount, @Quantity, @Description, @ReceivedBy);
END
GO

-- إجراء للحصول على تقرير الحالات
CREATE PROCEDURE sp_GetBeneficiariesReport
    @Status NVARCHAR(20) = NULL
AS
BEGIN
    SELECT 
        b.BeneficiaryID,
        b.NationalID,
        b.FullName,
        b.Nationality,
        b.Age,
        b.PhoneNumber,
        b.Address,
        b.EmploymentStatus,
        b.FamilyMembers,
        b.NeedType,
        b.ApplicationStatus,
        b.RegistrationDate,
        u.FullName AS ApprovedByName
    FROM Beneficiaries b
    LEFT JOIN Users u ON b.ApprovedBy = u.UserID
    WHERE (@Status IS NULL OR b.ApplicationStatus = @Status)
    ORDER BY b.RegistrationDate DESC;
END
GO

-- إجراء للحصول على تقرير التبرعات
CREATE PROCEDURE sp_GetDonationsReport
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @DonationType NVARCHAR(50) = NULL
AS
BEGIN
    SELECT 
        d.DonationID,
        don.DonorName,
        don.PhoneNumber,
        d.DonationType,
        d.DonationMethod,
        d.Amount,
        d.Quantity,
        d.Description,
        d.DonationDate,
        d.Status,
        u.FullName AS ReceivedByName
    FROM Donations d
    INNER JOIN Donors don ON d.DonorID = don.DonorID
    LEFT JOIN Users u ON d.ReceivedBy = u.UserID
    WHERE (@StartDate IS NULL OR d.DonationDate >= @StartDate)
      AND (@EndDate IS NULL OR d.DonationDate <= @EndDate)
      AND (@DonationType IS NULL OR d.DonationType = @DonationType)
    ORDER BY d.DonationDate DESC;
END
GO

PRINT 'تم إنشاء قاعدة البيانات بنجاح!';
